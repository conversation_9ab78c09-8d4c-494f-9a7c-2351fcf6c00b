events {
    worker_connections 1024;
}

http {
    upstream verdaccio {
        server verdaccio-local:4873;
    }

    server {
        listen 80;
        server_name localhost;

        # Serve Verd<PERSON>cio at /packages path
        location ~ ^/packages/(.*)$ {
            proxy_set_header Host $host:8080;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host:8080;
            proxy_set_header X-Forwarded-Port 8080;
            proxy_pass http://verdaccio/$1;
            proxy_redirect off;
        }

        # Optional: redirect root to packages
        location = / {
            return 301 /packages/;
        }
    }
} 